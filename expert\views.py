from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONParser
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from django.db.models import Q
from .models import Expert
from .serializers import ExpertSerializer


@extend_schema(
    tags=["Expert"]
)
class ExpertViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Expert model providing CRUD operations.
    Supports file uploads for certifications field.
    """
    queryset = Expert.objects.all()
    serializer_class = ExpertSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [MultiPartParser, FormParser, JSONParser]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]
    filterset_fields = ['code','name','affiliation','specialization','experienceYears','status']
    search_fields = ['code','name','affiliation','specialization','experienceYears','status']

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        code = request.query_params.get('code')
        name = request.query_params.get('name')
        affiliation = request.query_params.get('affiliation')
        specialization = request.query_params.get('specialization')
        experienceYears = request.query_params.get('experienceYears')
        status = request.query_params.get('status')
        userId = request.query_params.get('userId')
        
        if code:
            queryset = queryset.filter(code=code)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if affiliation:
            queryset = queryset.filter(affiliation__icontains=affiliation)
        if specialization:
            queryset = queryset.filter(specialization=specialization)
        if experienceYears:
            queryset = queryset.filter(experienceYears__icontains=experienceYears)
        if status:
            queryset = queryset.filter(status=status)
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
